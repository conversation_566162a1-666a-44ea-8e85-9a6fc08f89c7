<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">

    <!-- Background circle with gradient effect -->
    <path
        android:fillColor="#FFFBF0"
        android:pathData="M60,60m-55,0a55,55 0,1 1,110 0a55,55 0,1 1,-110 0" />

    <!-- Outer ring -->
    <path
        android:fillColor="#FF6B9D"
        android:pathData="M60,60m-50,0a50,50 0,1 1,100 0a50,50 0,1 1,-100 0"
        android:strokeColor="#FF6B9D"
        android:strokeWidth="2" />

    <!-- Medical cross background -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M60,60m-40,0a40,40 0,1 1,80 0a40,40 0,1 1,-80 0" />

    <!-- Medical cross - vertical bar -->
    <path
        android:fillColor="#E53E3E"
        android:pathData="M52,35L68,35L68,85L52,85Z" />

    <!-- Medical cross - horizontal bar -->
    <path
        android:fillColor="#E53E3E"
        android:pathData="M35,52L85,52L85,68L35,68Z" />

    <!-- Heart symbol overlay -->
    <path
        android:fillColor="#FF6B9D"
        android:pathData="M60,45C57,42 53,42 50,45C47,48 47,52 50,55L60,65L70,55C73,52 73,48 70,45C67,42 63,42 60,45Z" />

    <!-- Vital signs line (heartbeat) -->
    <path
        android:fillColor="none"
        android:strokeColor="#38B2AC"
        android:strokeWidth="3"
        android:pathData="M25,75L35,75L40,65L45,85L50,55L55,75L90,75" />

    <!-- Small medical icons around the border -->
    <!-- Stethoscope representation -->
    <path
        android:fillColor="#9F7AEA"
        android:pathData="M25,35m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0" />

    <!-- Thermometer representation -->
    <path
        android:fillColor="#FF8C00"
        android:pathData="M95,35m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0" />

    <!-- Baby icon representation -->
    <path
        android:fillColor="#FBD38D"
        android:pathData="M25,85m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0" />

    <!-- Scale/weight representation -->
    <path
        android:fillColor="#2D3748"
        android:pathData="M95,85m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0" />
</vector>
