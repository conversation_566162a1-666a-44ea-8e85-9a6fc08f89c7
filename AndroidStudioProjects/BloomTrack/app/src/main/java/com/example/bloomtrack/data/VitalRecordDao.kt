package com.example.bloomtrack.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface VitalRecordDao {
    
    @Query("SELECT * FROM vital_records ORDER BY timestamp DESC")
    fun getAllVitalRecords(): Flow<List<VitalRecord>>
    
    @Query("SELECT * FROM vital_records WHERE id = :id")
    suspend fun getVitalRecordById(id: String): VitalRecord?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVitalRecord(vitalRecord: VitalRecord)
    
    @Update
    suspend fun updateVitalRecord(vitalRecord: VitalRecord)
    
    @Delete
    suspend fun deleteVitalRecord(vitalRecord: VitalRecord)
    
    @Query("DELETE FROM vital_records WHERE id = :id")
    suspend fun deleteVitalRecordById(id: String)
    
    @Query("DELETE FROM vital_records")
    suspend fun deleteAllVitalRecords()
    
    @Query("SELECT COUNT(*) FROM vital_records")
    suspend fun getVitalRecordCount(): Int
    
    @Query("SELECT * FROM vital_records WHERE timestamp >= :startDate AND timestamp <= :endDate ORDER BY timestamp DESC")
    fun getVitalRecordsByDateRange(startDate: String, endDate: String): Flow<List<VitalRecord>>
}
