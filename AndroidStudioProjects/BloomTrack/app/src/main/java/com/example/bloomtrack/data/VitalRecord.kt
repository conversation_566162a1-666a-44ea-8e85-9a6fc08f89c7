package com.example.bloomtrack.data

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

data class VitalRecord(
    val id: String = UUID.randomUUID().toString(),
    val heartRate: Int? = null, // bpm
    val systolicBP: Int? = null, // mmHg
    val diastolicBP: Int? = null, // mmHg
    val weight: Double? = null, // kg
    val babyKicks: Int? = null, // kicks count
    val timestamp: LocalDateTime = LocalDateTime.now()
) {
    fun getFormattedDate(): String {
        return timestamp.format(DateTimeFormatter.ofPattern("EEE, dd MMM yyyy hh:mm a"))
    }
    
    fun getFormattedBP(): String? {
        return if (systolicBP != null && diastolicBP != null) {
            "$systolicBP/$diastolicBP mmHg"
        } else null
    }
    
    fun getFormattedHeartRate(): String? {
        return heartRate?.let { "$it bpm" }
    }
    
    fun getFormattedWeight(): String? {
        return weight?.let { "$it kg" }
    }
    
    fun getFormattedKicks(): String? {
        return babyKicks?.let { "$it kicks" }
    }
}
