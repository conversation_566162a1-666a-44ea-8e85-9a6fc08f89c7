package com.example.bloomtrack.data

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector

enum class VitalType(
    val displayName: String,
    val unit: String,
    val icon: ImageVector,
    val color: Color,
    val hasSecondaryValue: Boolean = false,
    val secondaryUnit: String = ""
) {
    BLOOD_PRESSURE(
        displayName = "Blood Pressure",
        unit = "mmHg",
        icon = Icons.Default.Favorite,
        color = Color(0xFFE53E3E), // Red
        hasSecondaryValue = true,
        secondaryUnit = "mmHg"
    ),
    WEIGHT(
        displayName = "Weight",
        unit = "kg",
        icon = Icons.Default.MonitorWeight,
        color = Color(0xFF2D3748) // Black/Dark Gray
    ),
    HEART_RATE(
        displayName = "Heart Rate",
        unit = "bpm",
        icon = Icons.Default.FavoriteBorder,
        color = Color(0xFFFF8C00) // Orange
    ),
    BLOOD_SUGAR(
        displayName = "Blood Sugar",
        unit = "mg/dL",
        icon = Icons.Default.Bloodtype,
        color = Color(0xFF9F7AEA) // Purple
    ),
    TEMPERATURE(
        displayName = "Temperature",
        unit = "°F",
        icon = Icons.Default.Thermostat,
        color = Color(0xFF38B2AC) // Teal
    ),
    BABY_MOVEMENTS(
        displayName = "Baby Movements",
        unit = "kicks/hour",
        icon = Icons.Default.ChildCare,
        color = Color(0xFFFFFFFF) // White (will need background)
    ),
    MOOD_ENERGY(
        displayName = "Mood/Energy",
        unit = "/10",
        icon = Icons.Default.Mood,
        color = Color(0xFFFBD38D) // Yellow/Gold
    )
}
