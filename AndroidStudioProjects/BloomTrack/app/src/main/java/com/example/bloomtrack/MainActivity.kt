package com.example.bloomtrack

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.example.bloomtrack.ui.SplashScreen
import com.example.bloomtrack.ui.VitalsListScreen
import com.example.bloomtrack.ui.theme.BloomTrackTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen
        installSplashScreen()

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Check if opened from notification
        val openAddDialog = intent?.getBooleanExtra("OPEN_ADD_DIALOG", false) ?: false

        setContent {
            BloomTrackTheme {
                var showSplash by remember { mutableStateOf(!openAddDialog) } // Skip splash if from notification

                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    if (showSplash) {
                        SplashScreen(
                            onSplashFinished = { showSplash = false }
                        )
                    } else {
                        VitalsListScreen(
                            openAddDialogInitially = openAddDialog
                        )
                    }
                }
            }
        }
    }

    override fun onNewIntent(intent: android.content.Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)

        // Handle notification click when app is already running
        val openAddDialog = intent?.getBooleanExtra("OPEN_ADD_DIALOG", false) ?: false
        if (openAddDialog) {
            // Recreate activity to trigger add dialog
            recreate()
        }
    }
}