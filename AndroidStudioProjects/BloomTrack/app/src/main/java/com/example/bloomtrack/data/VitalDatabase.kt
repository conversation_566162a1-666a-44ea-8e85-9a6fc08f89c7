package com.example.bloomtrack.data

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context

@Database(
    entities = [VitalRecord::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class VitalDatabase : RoomDatabase() {
    
    abstract fun vitalRecordDao(): VitalRecordDao
    
    companion object {
        @Volatile
        private var INSTANCE: VitalDatabase? = null
        
        fun getDatabase(context: Context): VitalDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    VitalDatabase::class.java,
                    "vital_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
