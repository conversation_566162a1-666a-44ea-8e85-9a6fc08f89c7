package com.example.bloomtrack.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.example.bloomtrack.data.VitalType
import com.example.bloomtrack.ui.theme.BloomPink

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddVitalDialog(
    onDismiss: () -> Unit,
    onAddVital: (VitalType, Double, Double?, String) -> Unit
) {
    var selectedVitalType by remember { mutableStateOf(VitalType.BLOOD_PRESSURE) }
    var primaryValue by remember { mutableStateOf("") }
    var secondaryValue by remember { mutableStateOf("") }
    var notes by remember { mutableStateOf("") }
    var expanded by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = MaterialTheme.shapes.large
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Add New Vital",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                // Vital Type Dropdown
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    OutlinedTextField(
                        value = selectedVitalType.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Vital Type") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        VitalType.entries.forEach { vitalType ->
                            DropdownMenuItem(
                                text = {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        // Color indicator
                                        Box(
                                            modifier = Modifier
                                                .size(16.dp)
                                                .clip(CircleShape)
                                                .background(
                                                    if (vitalType == VitalType.BABY_MOVEMENTS) {
                                                        BloomPink
                                                    } else {
                                                        vitalType.color
                                                    }
                                                )
                                        )
                                        Spacer(modifier = Modifier.width(12.dp))
                                        Text(vitalType.displayName)
                                    }
                                },
                                onClick = {
                                    selectedVitalType = vitalType
                                    expanded = false
                                    // Reset values when changing type
                                    primaryValue = ""
                                    secondaryValue = ""
                                }
                            )
                        }
                    }
                }
                
                // Primary Value Input
                OutlinedTextField(
                    value = primaryValue,
                    onValueChange = { primaryValue = it },
                    label = { 
                        Text(
                            if (selectedVitalType.hasSecondaryValue) 
                                "Systolic (${selectedVitalType.unit})" 
                            else 
                                "Value (${selectedVitalType.unit})"
                        ) 
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )
                
                // Secondary Value Input (for blood pressure)
                if (selectedVitalType.hasSecondaryValue) {
                    OutlinedTextField(
                        value = secondaryValue,
                        onValueChange = { secondaryValue = it },
                        label = { Text("Diastolic (${selectedVitalType.secondaryUnit})") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                
                // Notes Input
                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    label = { Text("Notes (optional)") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                
                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            val primary = primaryValue.toDoubleOrNull()
                            val secondary = if (selectedVitalType.hasSecondaryValue) 
                                secondaryValue.toDoubleOrNull() else null
                            
                            if (primary != null && 
                                (!selectedVitalType.hasSecondaryValue || secondary != null)) {
                                onAddVital(selectedVitalType, primary, secondary, notes)
                                onDismiss()
                            }
                        },
                        enabled = primaryValue.toDoubleOrNull() != null && 
                                (!selectedVitalType.hasSecondaryValue || secondaryValue.toDoubleOrNull() != null)
                    ) {
                        Text("Add")
                    }
                }
            }
        }
    }
}
