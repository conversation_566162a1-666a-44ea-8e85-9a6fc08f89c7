package com.example.bloomtrack.repository

import com.example.bloomtrack.data.VitalRecord
import com.example.bloomtrack.data.VitalRecordDao
import kotlinx.coroutines.flow.Flow
class VitalRepository(
    private val vitalRecordDao: VitalRecordDao
) {
    
    fun getAllVitalRecords(): Flow<List<VitalRecord>> {
        return vitalRecordDao.getAllVitalRecords()
    }
    
    suspend fun getVitalRecordById(id: String): VitalRecord? {
        return vitalRecordDao.getVitalRecordById(id)
    }
    
    suspend fun insertVitalRecord(vitalRecord: VitalRecord) {
        vitalRecordDao.insertVitalRecord(vitalRecord)
    }
    
    suspend fun updateVitalRecord(vitalRecord: VitalRecord) {
        vitalRecordDao.updateVitalRecord(vitalRecord)
    }
    
    suspend fun deleteVitalRecord(vitalRecord: VitalRecord) {
        vitalRecordDao.deleteVitalRecord(vitalRecord)
    }
    
    suspend fun deleteVitalRecordById(id: String) {
        vitalRecordDao.deleteVitalRecordById(id)
    }
    
    suspend fun deleteAllVitalRecords() {
        vitalRecordDao.deleteAllVitalRecords()
    }
    
    suspend fun getVitalRecordCount(): Int {
        return vitalRecordDao.getVitalRecordCount()
    }
    
    fun getVitalRecordsByDateRange(startDate: String, endDate: String): Flow<List<VitalRecord>> {
        return vitalRecordDao.getVitalRecordsByDateRange(startDate, endDate)
    }
}
