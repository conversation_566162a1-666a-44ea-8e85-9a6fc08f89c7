package com.example.bloomtrack.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.bloomtrack.ui.components.VitalCard
import com.example.bloomtrack.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VitalsListScreen(
    viewModel: VitalsViewModel = viewModel()
) {
    val vitalRecords by viewModel.vitalRecords

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        "Track My Pregnancy",
                        fontWeight = FontWeight.Bold,
                        color = FigmaText,
                        fontSize = 20.sp
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = FigmaBackground,
                    titleContentColor = FigmaText
                )
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { viewModel.showAddDialog() },
                containerColor = FigmaPurple,
                contentColor = Color.White,
                modifier = Modifier.size(56.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add vital",
                    modifier = Modifier.size(24.dp)
                )
            }
        },
        containerColor = FigmaBackground
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(top = 16.dp, bottom = 96.dp)
        ) {
            items(vitalRecords) { record ->
                VitalCard(record = record)
            }

            // Empty state
            if (vitalRecords.isEmpty()) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "No vitals recorded yet",
                                style = MaterialTheme.typography.bodyLarge,
                                color = FigmaGray,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Tap the + button to add your first vital record",
                                style = MaterialTheme.typography.bodyMedium,
                                color = FigmaGray.copy(alpha = 0.7f),
                                textAlign = androidx.compose.ui.text.style.TextAlign.Center
                            )
                        }
                    }
                }
            }
        }
    }

    // Add Vital Dialog
    if (viewModel.showAddDialog) {
        AddVitalDialog(
            onDismiss = { viewModel.hideAddDialog() },
            onAddVital = { heartRate, systolic, diastolic, weight, kicks ->
                viewModel.addVitalRecord(heartRate, systolic, diastolic, weight, kicks)
            }
        )
    }
}
