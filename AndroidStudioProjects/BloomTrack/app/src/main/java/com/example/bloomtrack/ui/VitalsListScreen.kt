package com.example.bloomtrack.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.bloomtrack.ui.components.VitalCard
import com.example.bloomtrack.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VitalsListScreen(
    viewModel: VitalsViewModel = viewModel()
) {
    val vitals by viewModel.vitals
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        "BloomTrack",
                        fontWeight = FontWeight.Bold,
                        color = BloomPink
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = BloomCream,
                    titleContentColor = BloomPink
                )
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { viewModel.showAddDialog() },
                containerColor = BloomPink,
                contentColor = Color.White,
                modifier = Modifier
                    .size(64.dp)
                    .clip(RoundedCornerShape(16.dp))
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add vital",
                    modifier = Modifier.size(28.dp)
                )
            }
        },
        containerColor = BloomCream
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            BloomCream,
                            BloomPinkLight.copy(alpha = 0.3f)
                        )
                    )
                )
                .padding(paddingValues)
                .padding(top = 24.dp) // Added top padding
                .padding(horizontal = 20.dp)
        ) {
            // Header
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                shape = RoundedCornerShape(20.dp)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Pregnancy Vitals Tracker",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = BloomPink
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Keep track of your health during pregnancy",
                        style = MaterialTheme.typography.bodyMedium,
                        color = BloomGray,
                        textAlign = TextAlign.Center
                    )
                }
            }
            
            // Vitals List
            if (vitals.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White.copy(alpha = 0.8f)
                        ),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center,
                            modifier = Modifier.padding(32.dp)
                        ) {
                            Text(
                                text = "No vitals recorded yet",
                                style = MaterialTheme.typography.bodyLarge,
                                color = BloomGray,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Tap the + button to add your first vital",
                                style = MaterialTheme.typography.bodyMedium,
                                color = BloomGray.copy(alpha = 0.7f),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    contentPadding = PaddingValues(bottom = 100.dp) // Space for FAB
                ) {
                    items(vitals) { vital ->
                        VitalCard(
                            vital = vital,
                            onDelete = { vitalId ->
                                viewModel.deleteVital(vitalId)
                            }
                        )
                    }
                }
            }
        }
    }
    
    // Add Vital Dialog
    if (viewModel.showAddDialog) {
        AddVitalDialog(
            onDismiss = { viewModel.hideAddDialog() },
            onAddVital = { type, primary, secondary, notes ->
                viewModel.addVital(type, primary, secondary, notes)
            }
        )
    }
}
