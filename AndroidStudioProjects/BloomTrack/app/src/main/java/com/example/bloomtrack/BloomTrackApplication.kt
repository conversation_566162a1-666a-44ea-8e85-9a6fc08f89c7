package com.example.bloomtrack

import android.app.Application
import androidx.work.*
import com.example.bloomtrack.worker.VitalReminderWorker
import java.util.concurrent.TimeUnit

class BloomTrackApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize WorkManager for periodic reminders
        setupPeriodicWork()
    }
    
    private fun setupPeriodicWork() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(false) // Allow even when battery is low for health reminders
            .build()

        val periodicWorkRequest = PeriodicWorkRequestBuilder<VitalReminderWorker>(
            5, TimeUnit.HOURS // Run every 5 hours as requested
        )
            .setConstraints(constraints)
            .setInitialDelay(5, TimeUnit.HOURS) // Wait 5 hours before first run
            .build()

        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
            "vital_reminder_work_5h",
            ExistingPeriodicWorkPolicy.REPLACE, // Replace existing work to update timing
            periodicWorkRequest
        )
    }
}
