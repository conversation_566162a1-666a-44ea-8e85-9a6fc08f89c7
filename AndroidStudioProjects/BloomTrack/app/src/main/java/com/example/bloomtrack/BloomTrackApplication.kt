package com.example.bloomtrack

import android.app.Application
import androidx.work.*
import com.example.bloomtrack.worker.VitalReminderWorker
import java.util.concurrent.TimeUnit

class BloomTrackApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize WorkManager for periodic reminders
        setupPeriodicWork()
    }
    
    private fun setupPeriodicWork() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val periodicWorkRequest = PeriodicWorkRequestBuilder<VitalReminderWorker>(
            24, TimeUnit.HOURS // Run once every 24 hours
        )
            .setConstraints(constraints)
            .setInitialDelay(1, TimeUnit.HOURS) // Wait 1 hour before first run
            .build()
        
        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
            "vital_reminder_work",
            ExistingPeriodicWorkPolicy.KEEP,
            periodicWorkRequest
        )
    }
}
