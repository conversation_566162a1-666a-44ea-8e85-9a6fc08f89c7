package com.example.bloomtrack.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Figma design colors - Purple theme
val FigmaPurple = Color(0xFF8B5CF6)
val FigmaPurpleLight = Color(0xFFE0C3FC)
val FigmaPurpleDark = Color(0xFF7C3AED)
val FigmaCardPurple = Color(0xFFDDD6FE)
val FigmaBackground = Color(0xFFF8FAFC)
val FigmaWhite = Color(0xFFFFFFFF)
val FigmaGray = Color(0xFF64748B)
val FigmaGrayLight = Color(0xFFF1F5F9)
val FigmaText = Color(0xFF1E293B)
val FigmaTextSecondary = Color(0xFF64748B)