package com.example.bloomtrack.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Custom colors for pregnancy app - Updated palette
val BloomPink = Color(0xFFFF6B9D)
val BloomPinkLight = Color(0xFFFFF0F5)
val BloomPinkDark = Color(0xFFE91E63)
val BloomPurple = Color(0xFF8E44AD)
val BloomPurpleLight = Color(0xFFF3E5F5)
val BloomTeal = Color(0xFF26A69A)
val BloomTealLight = Color(0xFFE0F2F1)
val BloomMint = Color(0xFF81C784)
val BloomMintLight = Color(0xFFE8F5E8)
val BloomCream = Color(0xFFFFFBF0)
val BloomGray = Color(0xFF757575)
val BloomGrayLight = Color(0xFFF5F5F5)