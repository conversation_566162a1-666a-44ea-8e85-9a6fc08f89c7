package com.example.bloomtrack.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.bloomtrack.data.VitalRecord
import com.example.bloomtrack.repository.VitalRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDateTime
class VitalsViewModel(
    private val repository: VitalRepository
) : ViewModel() {

    private val _vitalRecords = MutableStateFlow<List<VitalRecord>>(emptyList())
    val vitalRecords: StateFlow<List<VitalRecord>> = _vitalRecords.asStateFlow()

    private val _showAddDialog = MutableStateFlow(false)
    val showAddDialog: StateFlow<Boolean> = _showAddDialog.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        loadVitalRecords()
        // Add sample data if database is empty
        viewModelScope.launch {
            if (repository.getVitalRecordCount() == 0) {
                addSampleData()
            }
        }
    }

    private fun loadVitalRecords() {
        viewModelScope.launch {
            repository.getAllVitalRecords().collect { records ->
                _vitalRecords.value = records
            }
        }
    }

    fun addVitalRecord(
        heartRate: Int?,
        systolicBP: Int?,
        diastolicBP: Int?,
        weight: Double?,
        babyKicks: Int?
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val newRecord = VitalRecord(
                    heartRate = heartRate,
                    systolicBP = systolicBP,
                    diastolicBP = diastolicBP,
                    weight = weight,
                    babyKicks = babyKicks,
                    timestamp = LocalDateTime.now()
                )
                repository.insertVitalRecord(newRecord)
            } catch (e: Exception) {
                // Handle error - could emit to error state
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun deleteVitalRecord(recordId: String) {
        viewModelScope.launch {
            try {
                repository.deleteVitalRecordById(recordId)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }

    fun showAddDialog() {
        _showAddDialog.value = true
    }

    fun hideAddDialog() {
        _showAddDialog.value = false
    }

    private suspend fun addSampleData() {
        val sampleRecords = listOf(
            VitalRecord(
                heartRate = 90,
                systolicBP = 120,
                diastolicBP = 80,
                weight = 68.0,
                babyKicks = 15,
                timestamp = LocalDateTime.of(2025, 1, 13, 3, 45)
            ),
            VitalRecord(
                heartRate = 87,
                systolicBP = 128,
                diastolicBP = 86,
                weight = 75.0,
                babyKicks = 9,
                timestamp = LocalDateTime.of(2025, 1, 12, 10, 22)
            )
        )

        sampleRecords.forEach { record ->
            repository.insertVitalRecord(record)
        }
    }
}
