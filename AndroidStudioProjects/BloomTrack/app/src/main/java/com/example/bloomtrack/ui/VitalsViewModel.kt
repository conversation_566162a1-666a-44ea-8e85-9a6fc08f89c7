package com.example.bloomtrack.ui

import androidx.lifecycle.ViewModel
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import com.example.bloomtrack.data.VitalRecord
import java.time.LocalDateTime

class VitalsViewModel : ViewModel() {

    private val _vitalRecords = mutableStateOf<List<VitalRecord>>(emptyList())
    val vitalRecords: State<List<VitalRecord>> = _vitalRecords

    var showAddDialog by mutableStateOf(false)
        private set

    init {
        // Add sample data matching Figma design
        addSampleData()
    }

    fun addVitalRecord(
        heartRate: Int?,
        systolicBP: Int?,
        diastolicBP: Int?,
        weight: Double?,
        babyKicks: Int?
    ) {
        val newRecord = VitalRecord(
            heartRate = heartRate,
            systolicBP = systolicBP,
            diastolicBP = diastolicBP,
            weight = weight,
            babyKicks = babyKicks,
            timestamp = LocalDateTime.now()
        )

        _vitalRecords.value = (_vitalRecords.value + newRecord).sortedByDescending { it.timestamp }
    }

    fun deleteVitalRecord(recordId: String) {
        _vitalRecords.value = _vitalRecords.value.filter { it.id != recordId }
    }

    fun showAddDialog() {
        showAddDialog = true
    }

    fun hideAddDialog() {
        showAddDialog = false
    }

    private fun addSampleData() {
        val sampleRecords = listOf(
            VitalRecord(
                heartRate = 90,
                systolicBP = 120,
                diastolicBP = 80,
                weight = 68.0,
                babyKicks = 15,
                timestamp = LocalDateTime.of(2025, 1, 13, 3, 45)
            ),
            VitalRecord(
                heartRate = 87,
                systolicBP = 128,
                diastolicBP = 86,
                weight = 75.0,
                babyKicks = 9,
                timestamp = LocalDateTime.of(2025, 1, 12, 10, 22)
            )
        )

        _vitalRecords.value = sampleRecords.sortedByDescending { it.timestamp }
    }
}
