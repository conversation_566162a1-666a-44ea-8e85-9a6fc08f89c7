package com.example.bloomtrack.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.bloomtrack.data.VitalEntry
import com.example.bloomtrack.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VitalCard(
    vital: VitalEntry,
    onDelete: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon
            Icon(
                imageVector = vital.type.icon,
                contentDescription = vital.type.displayName,
                modifier = Modifier.size(44.dp),
                tint = BloomPink
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = vital.type.displayName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = BloomGray
                )

                Text(
                    text = vital.getFormattedValue(),
                    style = MaterialTheme.typography.bodyLarge,
                    color = BloomPink,
                    fontWeight = FontWeight.Bold
                )

                if (vital.notes.isNotEmpty()) {
                    Text(
                        text = vital.notes,
                        style = MaterialTheme.typography.bodySmall,
                        color = BloomGray.copy(alpha = 0.8f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Text(
                    text = "${vital.getFormattedDate()} at ${vital.getFormattedTime()}",
                    style = MaterialTheme.typography.bodySmall,
                    color = BloomGray.copy(alpha = 0.6f)
                )
            }
            
            // Delete button
            IconButton(
                onClick = { onDelete(vital.id) }
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "Delete vital",
                    tint = Color(0xFFE57373),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}
