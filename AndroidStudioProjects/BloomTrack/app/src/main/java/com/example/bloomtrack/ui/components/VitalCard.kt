package com.example.bloomtrack.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.bloomtrack.data.VitalRecord
import com.example.bloomtrack.ui.theme.*

@Composable
fun VitalCard(
    record: VitalRecord,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            FigmaPurpleLight.copy(alpha = 0.3f),
                            FigmaCardPurple.copy(alpha = 0.5f)
                        )
                    )
                )
                .padding(16.dp)
        ) {
            // Top row with vitals
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Heart Rate
                VitalItem(
                    icon = Icons.Default.Favorite,
                    value = record.getFormattedHeartRate() ?: "--",
                    modifier = Modifier.weight(1f)
                )

                // Blood Pressure
                VitalItem(
                    icon = Icons.Default.MonitorHeart,
                    value = record.getFormattedBP() ?: "--",
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Bottom row with vitals
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Weight
                VitalItem(
                    icon = Icons.Default.MonitorWeight,
                    value = record.getFormattedWeight() ?: "--",
                    modifier = Modifier.weight(1f)
                )

                // Baby Kicks
                VitalItem(
                    icon = Icons.Default.ChildCare,
                    value = record.getFormattedKicks() ?: "--",
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Date/Time bar
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = FigmaPurple,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(vertical = 8.dp, horizontal = 12.dp)
            ) {
                Text(
                    text = record.getFormattedDate(),
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun VitalItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = FigmaPurple,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = FigmaText
        )
    }
}
