package com.example.bloomtrack.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext

private val DarkColorScheme = darkColorScheme(
    primary = BloomPink,
    secondary = BloomPurple,
    tertiary = BloomTeal,
    primaryContainer = BloomPinkDark,
    secondaryContainer = BloomPurpleLight,
    background = Color(0xFF1A1A1A),
    surface = Color(0xFF2D2D2D),
    surfaceVariant = BloomGrayLight
)

private val LightColorScheme = lightColorScheme(
    primary = BloomPink,
    secondary = BloomPurple,
    tertiary = BloomTeal,
    primaryContainer = BloomPinkLight,
    secondaryContainer = BloomPurpleLight,
    background = BloomCream,
    surface = Color.White,
    surfaceVariant = BloomGrayLight,
    onSurfaceVariant = BloomGray
)

@Composable
fun BloomTrackTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}